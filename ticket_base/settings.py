"""
Django settings for ticket_base project.

Generated by 'django-admin startproject' using Django 3.2.7.

For more information on this file, see
https://docs.djangoproject.com/en/3.2/topics/settings/

For the full list of settings and their values, see
https://docs.djangoproject.com/en/3.2/ref/settings/
"""
import smtplib
from datetime import timedelta
from pathlib import Path
import os

from celery.schedules import crontab
from django.core.mail import send_mail

from django.core.cache.backends.redis import RedisCache
from django.core.management.utils import get_random_secret_key
from decouple import config





# Build paths inside the project like this: BASE_DIR / 'subdir'.
BASE_DIR = Path(__file__).resolve().parent.parent
PROJECT_ROOT = os.path.abspath(os.path.dirname(__file__))
# Define a base directory for log files
LOG_DIR = Path(BASE_DIR) / 'logs'
# Ensure log directory exists
LOG_DIR.mkdir(exist_ok=True)
# Quick-start development settings - unsuitable for production
# See https://docs.djangoproject.com/en/3.2/howto/deployment/checklist/
TIME_ZONE = 'America/Chicago'
# SECURITY WARNING: keep the secret key used in production secret!
SECRET_KEY = config("DJANGO_SECRET_KEY", get_random_secret_key())
USE_TZ = True

# MAINTENANCE_MODE = True
# SECURITY WARNING: don't run with debug turned on in production!
DEBUG = True
STRIPE_PUBLIC_KEY = config("STRIPE_PUB_TEST")
STRIPE_PRIVATE_KEY = config("STRIPE_PRI_TEST")
SESSION_COOKIE_SECURE = True
CSRF_COOKIE_SECURE = True

ALLOWED_HOSTS = ['ticketpros.io',  '***************', '"DJANGO_ALLOWED_HOSTS"', '************', 'www.ticketpros.io', '0.0.0.0', '127.0.0.1', 'localhost']

MAINTENANCE_MODE_IGNORE_IP_ADDRESSES = [
    '127.0.0.1',  # Localhost for local development
    '*************',  # Replace with the admin's public IP address
]

MAINTENANCE_MODE_IGNORE_STAFF = True
MAINTENANCE_MODE_IGNORE_SUPERUSER = True

CORS_ORIGIN_ALLOW_ALL = True
CORS_ALLOW_CREDENTIALS = True

SIMPLE_JWT = {
    'ACCESS_TOKEN_LIFETIME': timedelta(minutes=5),
    'REFRESH_TOKEN_LIFETIME': timedelta(days=7),
    'ROTATE_REFRESH_TOKENS': True,
    'BLACKLIST_AFTER_ROTATION': True,
    'ALGORITHM': 'HS256',
    'SIGNING_KEY': SECRET_KEY,
}

AUTHENTICATION_BACKENDS = ['ticket_app.backends.backends.EmailBackend', 'django.contrib.auth.backends.ModelBackend',
                           'guardian.backends.ObjectPermissionBackend']

EMAIL_BACKEND = 'django.core.mail.backends.smtp.EmailBackend'
EMAIL_HOST = 'smtp.gmail.com'
EMAIL_PORT = 465  # Use 465 for SSL
EMAIL_USE_SSL = True  # Use SSL (for Gmail, this is typically required)
EMAIL_USE_TLS = False  # Disable TLS since we're using SSL
EMAIL_HOST_USER = '<EMAIL>'
EMAIL_HOST_PASSWORD = config("EMAIL_PASS")
DEFAULT_FILE_STORAGE = 'django.core.files.storage.FileSystemStorage'


# Custom error handlers
HANDLER404 = 'ticket_app.views.custom_404_view'
HANDLER500 = 'ticket_app.views.custom_500_view'
HANDLER403 = 'ticket_app.views.custom_403_view'
HANDLER400 = 'ticket_app.views.custom_400_view'


DATA_UPLOAD_MAX_NUMBER_FIELDS = 50000
FILE_UPLOAD_MAX_MEMORY_SIZE = 5 * 1024 * 1024        # 5MB per file
DATA_UPLOAD_MAX_MEMORY_SIZE = 1024 * 1024 * 1024     # 1GB total
DATA_UPLOAD_MAX_NUMBER_FILES = 5000
# Optional: Set temporary file directory for large uploads
FILE_UPLOAD_TEMP_DIR = os.path.join(BASE_DIR, 'tmp')

# Ensure the directory exists
os.makedirs(FILE_UPLOAD_TEMP_DIR, exist_ok=True)
# Application definition
AUTH_USER_MODEL = 'ticket_app.TicketUser'
TAILWIND_APP_NAME = 'frontend_v2'
INSTALLED_APPS = [
    'jazzmin',
    'rest_framework',
    'rest_framework.authtoken',
    'tailwind',
    'bootstrap_modal_forms',
    'django.contrib.admin',
    'django.contrib.auth',
    'django.contrib.contenttypes',
    'django.contrib.sessions',
    'django.contrib.messages',
    'django.contrib.staticfiles',
    'ticket_app.apps.TicketAppConfig',
    'widget_tweaks',
    'guardian',
    'easy_thumbnails',
    'django_filters',
    'crispy_forms',
    'crispy_bootstrap4',
    'celery',
    'django_celery_results',
    'django_celery_beat',
    'celery_progress',
    'timepunch',
    'maintenance_mode',

]

# settings.py
REST_FRAMEWORK = {
    'DEFAULT_AUTHENTICATION_CLASSES': (
        'rest_framework_simplejwt.authentication.JWTAuthentication',
    ),
    'DEFAULT_PERMISSION_CLASSES': (
        'rest_framework.permissions.IsAuthenticated',
    ),
}



CELERY_BEAT_SCHEDULE = {

}



CELERY_BROKER_URL = 'redis://localhost:6379/0'
CELERY_ACCEPT_CONTENT = ['json']
CELERY_TASK_SERIALIZER = 'json'
CELERY_RESULT_BACKEND = 'django-db'
CELERY_TIMEZONE = 'America/Chicago'
CELERY_RESULT_EXTENDED = True

CACHES = {
    'default': {
        'BACKEND': 'django_redis.cache.RedisCache',
        'LOCATION': 'redis://localhost:6379/1',  # Adjust as per your Redis configuration
        'OPTIONS': {
            'CLIENT_CLASS': 'django_redis.client.DefaultClient',
        }
    }
}


THUMBNAIL_PROCESSORS = (
    'easy_thumbnails.processors.colorspace',
    'easy_thumbnails.processors.autocrop',
    'easy_thumbnails.processors.scale_and_crop',
    'easy_thumbnails.processors.filters',
)

MIDDLEWARE = [
    'django.middleware.security.SecurityMiddleware',
    'django.contrib.sessions.middleware.SessionMiddleware',
    'corsheaders.middleware.CorsMiddleware',
    'django.middleware.common.CommonMiddleware',
    'django.middleware.csrf.CsrfViewMiddleware',
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'django.contrib.messages.middleware.MessageMiddleware',
    'django.middleware.clickjacking.XFrameOptionsMiddleware',
    'maintenance_mode.middleware.MaintenanceModeMiddleware',
    'ticket_app.middleware.ImpersonateMiddleware',

]

# Message settings
from django.contrib.messages import constants as message_constants
MESSAGE_STORAGE = 'django.contrib.messages.storage.session.SessionStorage'
MESSAGE_TAGS = {
    message_constants.DEBUG: 'debug',
    message_constants.INFO: 'info',
    message_constants.SUCCESS: 'success',
    message_constants.WARNING: 'warning',
    message_constants.ERROR: 'danger',
}

# SESSION_ENGINE = 'django.contrib.sessions.backends.cache'

ROOT_URLCONF = 'ticket_base.urls'

TEMPLATES = [
    {
        'BACKEND': 'django.template.backends.django.DjangoTemplates',
        'DIRS': [BASE_DIR / 'templates'],
        'APP_DIRS': True,
        'OPTIONS': {
            'context_processors': [
                'django.template.context_processors.debug',
                'django.template.context_processors.request',
                'django.contrib.auth.context_processors.auth',
                'django.contrib.messages.context_processors.messages',
            ],
        },
    },
]

MAINTENANCE_MODE_GETTER = 'ticket_app.maintenance.is_maintenance_mode'



WSGI_APPLICATION = 'ticket_base.wsgi.application'
MEDIA_URL = '/media/'
if DEBUG:
    STATIC_ROOT = os.path.join(BASE_DIR, '/static')
    MEDIA_ROOT = os.path.join(BASE_DIR, 'media')
    STATICFILES_DIRS = [
        os.path.join(BASE_DIR, 'static')
    ]
else:
    STATIC_ROOT = 'static'
    MEDIA_ROOT = os.path.join(BASE_DIR, 'media')
STATIC_URL = '/static/'
# Database
# https://docs.djangoproject.com/en/3.2/ref/settings/#databases

if DEBUG:
    DATABASES = {
        'default': {
            'ENGINE': 'django.db.backends.sqlite3',
            'NAME': str(os.path.join(BASE_DIR, "db.sqlite3")),
        }
    }
else:
    DATABASES = {
        'default': {
            'ENGINE': 'django.db.backends.postgresql_psycopg2',
            'NAME': 'ticketdash',
            'USER': 'ticketadmin',
            'PASSWORD': 'GQ[23x:g:T*Y]PO.:09',
            'HOST': 'localhost',
            'PORT': '5432',
        }
    }

# Password validation
# https://docs.djangoproject.com/en/3.2/ref/settings/#auth-password-validators

AUTH_PASSWORD_VALIDATORS = [
    {
        'NAME': 'django.contrib.auth.password_validation.UserAttributeSimilarityValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.MinimumLengthValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.CommonPasswordValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.NumericPasswordValidator',
    },
]

# Internationalization
# https://docs.djangoproject.com/en/3.2/topics/i18n/

LANGUAGE_CODE = 'en-us'
LOGIN_URL = 'login'
LOGOUT_REDIRECT_URL = 'home'

LOGIN_REDIRECT_URL = 'home'

CRISPY_TEMPLATE_PACK = 'bootstrap4'

# Static files (CSS, JavaScript, Images)
# https://docs.djangoproject.com/en/3.2/howto/static-files/


# Default primary key field type
# https://docs.djangoproject.com/en/3.2/ref/settings/#default-auto-field

DEFAULT_AUTO_FIELD = 'django.db.models.BigAutoField'

LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'formatters': {
        'verbose': {
            'format': '{levelname} {asctime} {module} {process:d} {thread:d} {message}',
            'style': '{',
        },
        'simple': {
            'format': '{levelname} {message}',
            'style': '{',
        },
    },
    'handlers': {
        'file': {
            'level': 'INFO',
            'class': 'logging.handlers.RotatingFileHandler',
            'filename': os.path.join(LOG_DIR, 'django.log'),
            'maxBytes': 1024*1024*5,  # 5 MB
            'backupCount': 5,
            'formatter': 'verbose',
        },
        'console': {
            'level': 'DEBUG',
            'class': 'logging.StreamHandler',
            'formatter': 'simple',
        },
    },
    'loggers': {
        'django': {
            'handlers': ['file', 'console'] if DEBUG else ['file', 'console'],
            'level': 'DEBUG' if DEBUG else 'INFO',
            'propagate': True,
        },
        # Define other loggers below as needed
    },
}


JAZZMIN_SETTINGS = {
    # title of the window (Will default to current_admin_site.site_title if absent or None)
    "site_title": "Ticket Pros",

    # Title on the login screen (19 chars max) (defaults to current_admin_site.site_header if absent or None)
    "site_header": "Ticket Pros",

    # Title on the brand (19 chars max) (defaults to current_admin_site.site_header if absent or None)
    "site_brand": "Ticket Pros",

    # Logo to use for your site, must be present in static files, used for brand on top left
    "site_logo": '../media/logo/logo.png',

    # Logo to use for your site, must be present in static files, used for login form logo (defaults to site_logo)
    "login_logo": '../media/logo/small.png',

    # Logo to use for login form in dark themes (defaults to login_logo)
    "login_logo_dark": None,

    # CSS classes that are applied to the logo above
    "site_logo_classes": None,

    # Relative path to a favicon for your site, will default to site_logo if absent (ideally 32x32 px)
    "site_icon": None,

    # Welcome text on the login screen
    "welcome_sign": "Ticket Pros",

    # Copyright on the footer
    "copyright": "Ticket Pros LLC",

    # The model admin to search from the search bar, search bar omitted if excluded
    "search_model": "auth.User",

    # # Field name on user model that contains avatar ImageField/URLField/Charfield or a callable that receives the user
    # "user_avatar": None,

    ############
    # Top Menu #
    ############

    # Links to put along the top menu
    "topmenu_links": [

        # Url that gets reversed (Permissions can be added)
        {"name": "Home", "url": "admin:index", "permissions": ["auth.view_user"]},

        # model admin to link to (Permissions checked against model)
        {"model": "auth.User"},

        # App with dropdown menu to all its models pages (Permissions checked against models)

    ],

    #############
    # User Menu #
    #############

    # Additional links to include in the user menu on the top right ("app" url type is not allowed)
    "usermenu_links": [
        {"name": "Home Page", "url": "home", "new_window": True},
        {"model": "auth.user"}
    ],

    #############
    # Side Menu #
    #############

    # Whether to display the side menu
    "show_sidebar": True,

    # Whether to aut expand the menu
    "navigation_expanded": True,

    # Hide these apps when generating side menu e.g (auth)
    "hide_apps": [],

    # Hide these models when generating side menu (e.g auth.user)
    "hide_models": [],

    # List of apps (and/or models) to base side menu ordering off of (does not need to contain all apps/models)

    # Custom links to append to app groups, keyed on app name

    # Custom icons for side menu apps/models See https://fontawesome.com/icons?d=gallery&m=free&v=5.0.0,5.0.1,5.0.10,5.0.11,5.0.12,5.0.13,5.0.2,5.0.3,5.0.4,5.0.5,5.0.6,5.0.7,5.0.8,5.0.9,5.1.0,5.1.1,5.2.0,5.3.0,5.3.1,5.4.0,5.4.1,5.4.2,5.13.0,5.12.0,5.11.2,5.11.1,5.10.0,5.9.0,5.8.2,5.8.1,5.7.2,5.7.1,5.7.0,5.6.3,5.5.0,5.4.2
    # for the full list of 5.13.0 free icon classes
    "icons": {
        "auth": "fas fa-users-cog",
        "auth.user": "fas fa-user",
        "auth.Group": "fas fa-users",
        "ticket_app.truck": "fas fa-truck",
        "ticket_app.client": "fas fa-truck",
        "ticket_app.ticket": "fas fa-file-invoice",
        "ticket_app.TicketImage": "fas fa-file-image",
        "ticket_app.Project": "fas fa-book",
        "ticket_app.employees": "fas fa-user-circle",
        "ticket_app.debristype": "fas fa-leaf",
        "ticket_app.debrisarea": "fas fa-map",
    },
    # Icons that are used when one is not manually specified
    "default_icon_parents": "fas fa-chevron-circle-right",
    "default_icon_children": "fas fa-circle",

    #################
    # Related Modal #
    #################
    # Use modals instead of popups
    "related_modal_active": True,

    #############
    # UI Tweaks #
    #############
    # Relative paths to custom CSS/JS scripts (must be present in static files)
    "custom_css": None,
    "custom_js": None,
    # Whether to show the UI customizer on the sidebar
    "show_ui_builder": True,

    ###############
    # Change view #
    ###############
    # Render out the change view as a single form, or in tabs, current options are
    # - single
    # - horizontal_tabs (default)
    # - vertical_tabs
    # - collapsible
    # - carousel
    "changeform_format": "horizontal_tabs",
    # override change forms on a per modeladmin basis
    "changeform_format_overrides": {"auth.user": "collapsible", "auth.group": "vertical_tabs"},
    # Add a language dropdown into the admin
    "language_chooser": False,
}
